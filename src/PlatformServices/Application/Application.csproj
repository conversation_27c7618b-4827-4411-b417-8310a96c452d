<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <PlaywrightPlatform>all</PlaywrightPlatform>
    <GenerateRuntimeConfigurationFiles>true</GenerateRuntimeConfigurationFiles>
  </PropertyGroup>

  <ItemGroup>
	<FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.19.1" />
    <PackageReference Include="Cronos" Version="0.8.4" />
    <PackageReference Include="Elsa.Activities.Email" Version="2.14.1" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
    <PackageReference Include="Microsoft.Playwright" Version="1.43.0" />
    <PackageReference Include="NPOI" Version="2.7.1" />
    <PackageReference Include="Refit" Version="6.3.2" />
    <PackageReference Include="Refit.HttpClientFactory" Version="6.3.2" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="SSH.NET" Version="2024.1.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Coordinate.Core\Coordinate.Core.csproj" />
    <ProjectReference Include="..\Domain\Domain.csproj" />
  </ItemGroup>

</Project>
