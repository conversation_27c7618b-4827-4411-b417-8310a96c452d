using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.SimulationWorkFlow;

namespace Workflow.StabilityAnalysis.StartSimulation.Activities
{
    public class GetSimulationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GetSimulationActivity> _logger;

        public GetSimulationActivity(
            IClientsApiService clientsApiService,
            ILogger<GetSimulationActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulationId = context.GetTransientVariable<Domain.Messages.Commands.Simulation.StartSimulation>(Variables.Command).Id;

                var simulation = await _clientsApiService
                    .GetSimulationById(simulationId);

                if (simulation == null)
                {
                    _logger.LogSimulationNotFound(simulationId);
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(Variables.Simulation, simulation);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogGetSimulationError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
