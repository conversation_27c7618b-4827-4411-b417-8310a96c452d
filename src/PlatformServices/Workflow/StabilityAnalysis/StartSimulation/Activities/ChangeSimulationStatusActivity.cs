using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.SimulationWorkFlow;
using StabilityAnalysisVariables = Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.StabilityAnalysis.StartSimulation.Activities
{
    public class ChangeSimulationStatusActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<ChangeSimulationStatusActivity> _logger;

        public ChangeSimulationStatusActivity(
            IClientsApiService clientsApiService,
            ILogger<ChangeSimulationStatusActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulationId = context.GetTransientVariable<Domain.Messages.Commands.Simulation.StartSimulation>(Variables.Command).Id;
                var errorMessage = context.GetTransientVariable<string>(StabilityAnalysisVariables.Variables.ErrorMessage);

                var entityHasBeenUpdated = await _clientsApiService.UpdateSimulationStatus(new()
                {
                    Id = simulationId,
                    Status = Domain.Enums.SimulationStatus.Failed,
                    Event = string.IsNullOrEmpty(errorMessage) ? "Erro inesperado nos processos internos da simulação." : errorMessage
                });

                if (!entityHasBeenUpdated)
                {
                    _logger.LogChangeSimulationStatusWarning(simulationId);
                    return Fault($"There was a problem updating simulation {simulationId} status");
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogChangeSimulationStatusError(e);
                return Fault(e);
            }
        }
    }
}
