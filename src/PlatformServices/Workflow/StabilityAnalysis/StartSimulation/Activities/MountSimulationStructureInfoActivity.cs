using Application.Apis.Clients;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Apis.Clients.Response.Simulation.GetById;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using SimulationVariables = Workflow.Constants.SimulationWorkFlow.Variables;

namespace Workflow.StabilityAnalysis.StartSimulation.Activities
{
    public class MountSimulationStructureInfoActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<MountSimulationStructureInfoActivity> _logger;

        public MountSimulationStructureInfoActivity(IClientsApiService clientsApiService, ILogger<MountSimulationStructureInfoActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var simulation = context.GetTransientVariable<GetSimulationByIdResponse>(SimulationVariables.Simulation);

                var structureInfo = new StructureInfo
                {
                    SeismicCoefficient = simulation.SeismicCoefficient,
                    ShouldEvaluateDrainedCondition = simulation.ShouldEvaluateDrainedCondition,
                    ShouldEvaluatePseudoStaticCondition = simulation.ShouldEvaluatePseudoStaticCondition,
                    ShouldEvaluateUndrainedCondition = simulation.ShouldEvaluateUndrainedCondition,
                    Slide2Configuration = simulation.Slide2Configuration,
                    SafetyFactorTarget = simulation.SafetyFactorTarget,
                    Sections = new()
                };

                foreach (var simulationSection in simulation.Sections)
                {
                    var section = await _clientsApiService.GetSectionById(simulationSection.SectionId);

                    if (section == null)
                    {
                        _logger.LogSimulationSectionNotFound(simulationSection.SectionId);
                        return Outcome(OutcomeNames.Cancel);
                    }

                    structureInfo.StructureId = section.Structure.Id;
                    structureInfo.StructureName = section.Structure.Name;

                    var filesResult = DetermineFilesToUse(simulationSection, section);

                    if (filesResult == null || filesResult.SectionReview == null || filesResult.Dxf == null || filesResult.Sli == null)
                    {
                        _logger.LogReviewNotFoundForSectionId(simulationSection.SectionId);
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var sectionInfo = new SectionInfo()
                    {
                        SectionId = section.Id,
                        SectionName = section.Name,
                        Coordinates = section.Coordinates,
                        IsSkew = section.IsSkew,
                        SectionReviewData = filesResult,
                        BeachLength = simulationSection.BeachLength,
                        MinimumDrainedDepth = simulationSection.MinimumDrainedDepth,
                        MinimumUndrainedDepth = simulationSection.MinimumUndrainedDepth,
                        MinimumPseudoStaticDepth = simulationSection.MinimumPseudoStaticDepth,
                        UpstreamLinimetricRulerQuota = simulation.WaterTableVariation.HasValue ? simulation.UpstreamLinimetricRulerQuota + simulation.WaterTableVariation.Value : simulation.UpstreamLinimetricRulerQuota,
                        DownstreamLinimetricRulerQuota = simulation.WaterTableVariation.HasValue ? simulation.DownstreamLinimetricRulerQuota + simulation.WaterTableVariation.Value : simulation.DownstreamLinimetricRulerQuota,
                        Instruments = new()
                    };

                    foreach (var instrument in simulationSection.Instruments)
                    {
                        var instrumentData = new InstrumentData()
                        {
                            InstrumentId = instrument.InstrumentId,
                            MeasurementId = instrument.MeasurementId,
                            DryReading = instrument.Dry,
                            InstrumentDryType = instrument.InstrumentDryType,
                            InstrumentType = instrument.InstrumentType,
                            ReadingQuota = simulation.WaterTableVariation.HasValue ? instrument.Quota + simulation.WaterTableVariation.Value : instrument.Quota
                        };

                        sectionInfo.Instruments.Add(instrumentData);
                    }

                    structureInfo.Sections.Add(sectionInfo);
                }

                context.SetTransientVariable(Variables.StructureInfo, structureInfo);
                context.SetTransientVariable(Variables.StructureId, structureInfo.StructureId);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogMountSimulationStructureInfoError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private SectionReviewData DetermineFilesToUse(GetSimulationByIdSection simulationSection, GetSectionByIdResponse section)
        {
            if (!simulationSection.SectionReviewId.HasValue)
            {
                foreach (var review in section.Reviews.OrderByDescending(x => x.StartDate))
                {
                    if (AreFilesValid(review.Drawing, review.Sli))
                    {
                        return new()
                        {
                            SectionReview = review,
                            Dxf = review.Drawing,
                            Sli = review.Sli
                        };
                    }
                    else if (review.ConstructionStages.Any())
                    {
                        var currentStage = review.ConstructionStages
                                    .FirstOrDefault(x => x.IsCurrentStage);

                        if (currentStage != null && AreFilesValid(currentStage.Drawing, currentStage.Sli))
                        {
                            return new()
                            {
                                SectionReview = review,
                                ConstructionStage = currentStage,
                                Dxf = currentStage.Drawing,
                                Sli = currentStage.Sli
                            };
                        }

                        foreach (var stage in review.ConstructionStages.OrderByDescending(x => x.Stage))
                        {
                            if (AreFilesValid(stage.Drawing, stage.Sli))
                            {
                                return new()
                                {
                                    SectionReview = review,
                                    ConstructionStage = stage,
                                    Dxf = stage.Drawing,
                                    Sli = stage.Sli
                                };
                            }
                        }
                    }
                }
            }
            else if (!simulationSection.ConstructionStageId.HasValue)
            {
                var review = section.Reviews.FirstOrDefault(x => x.Id == simulationSection.SectionReviewId);

                return new()
                {
                    SectionReview = review,
                    Dxf = review.Drawing,
                    Sli = review.Sli
                };
            }

            var sectionReview = section.Reviews.FirstOrDefault(x => x.Id == simulationSection.SectionReviewId);
            var constructionStage = sectionReview?.ConstructionStages.FirstOrDefault(x => x.Id == simulationSection.ConstructionStageId);

            return new()
            {
                SectionReview = sectionReview,
                ConstructionStage = constructionStage,
                Dxf = constructionStage?.Drawing,
                Sli = constructionStage?.Sli
            };
        }

        private static bool AreFilesValid(Application.Apis._Shared.File dxf, Application.Apis._Shared.File sli)
        {
            return dxf != null
                    && !string.IsNullOrEmpty(dxf.Base64)
                    && sli != null
                    && !string.IsNullOrEmpty(sli.Base64);
        }
    }
}
