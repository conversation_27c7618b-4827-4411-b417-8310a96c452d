using Application.Apis.Clients;
using Domain.Enums;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Serilog;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class CreateDrainageAlertActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private const string _notificationMessageTemplate = "Atenção! O gradiente hidráulico no pé da estrutura, próximo à seção {0}, da estrutura {1} parece estar alto. É recomendado que seja feita uma inspeção de campo.";

        public CreateDrainageAlertActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

                if (sliFiles == null)
                {
                    return Done();
                }

                var sectionsAlreadyAlerted = new List<Guid>();

                foreach (var sliFile in sliFiles)
                {
                    if (sliFile.PipingAlertGenerated && !sectionsAlreadyAlerted.Any(x => x == sliFile.Section.SectionId))
                    {
                        var users = await _clientsApiService.GetUsersAsync(new()
                        {
                            StructureId = sliFile.Structure.StructureId,
                            Active = true,
                            Roles = new()
                            {
                                (int)Role.SuperSupport,
                                (int)Role.Support,
                                (int)Role.SuperAdministrator,
                                (int)Role.Administrator,
                                (int)Role.Operator,
                                (int)Role.Auditor
                            }
                        });

                        if (users == null || !users.Any())
                        {
                            return Done();
                        }

                        var notificationMessage = String.Format(
                            _notificationMessageTemplate,
                            sliFile.Section.SectionName,
                            sliFile.Structure.StructureName);

                        await _clientsApiService.AddNotification(new()
                        {
                            Users = users.Select(x => x.Id).ToList(),
                            NotificationMessage = notificationMessage,
                            NotificationTheme = NotificationTheme.DrainageAlert
                        });

                        sectionsAlreadyAlerted.Add(sliFile.Section.SectionId);
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in CreateDrainageAlertActivity");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
