using Application.Apis.Clients.Response.Package.GetById;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class ValidateSectionsFilesActivity : Activity
    {
        private readonly ILogger<ValidateSectionsFilesActivity> _logger;
        private GetPackageByIdResponse _package;

        public ValidateSectionsFilesActivity(ILogger<ValidateSectionsFilesActivity> logger)
        {
            _logger = logger;
        }

        protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
        {
            try
            {
                var sections = context.GetTransientVariable<List<GetSectionByIdResponse>>(PackageVariables.Sections);
                var sectionsToCalculate = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).SectionIds;
                _package = context.GetTransientVariable<GetPackageByIdResponse>(PackageVariables.Package);

                foreach (var section in _package.Sections)
                {
                    var dbSection = sections
                        .FirstOrDefault(x => x.Id == section.Id);

                    if (dbSection == null && !sectionsToCalculate.Any(x => x == section.Id))
                    {
                        continue;
                    }
                    else if (dbSection == null)
                    {
                        _logger.LogValidationSectionNotFound(section.Name, _package.Date);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois seção {section.Name} não foi encontrada.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (section.SectionReview == null)
                    {
                        _logger.LogValidationSectionNoValidReviews(section.Name, _package.Date);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois seção {section.Name} não tem revisões válidas.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var dbReview = dbSection.Reviews.FirstOrDefault(x => x.Id == section.SectionReview.Id);

                    if (dbReview == null)
                    {
                        _logger.LogValidationSelectedReviewNotFound(section.Name, _package.Date);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois não foi possível encontrar a revisão selecionada.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (dbReview.ConstructionStages.Any())
                    {
                        var constructionStage = dbReview.ConstructionStages
                            .FirstOrDefault(x => x.Id == section.ConstructionStage?.Id);

                        if (constructionStage == null)
                        {
                            _logger.LogValidationConstructionStageNotFound(section.Name, _package.Date);
                            context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois não foi possível encontrar a etapa de obra selecionada.");
                            return Outcome(OutcomeNames.Cancel);
                        }

                        if (!AreFilesValid(constructionStage.Drawing, constructionStage.Sli))
                        {
                            _logger.LogValidationConstructionStageInvalidFiles(section.Name, _package.Date);
                            context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois a etapa de obra selecionada não possui arquivos válidos.");
                            return Outcome(OutcomeNames.Cancel);
                        }
                    }
                    else if (!AreFilesValid(dbReview.Drawing, dbReview.Sli))
                    {
                        _logger.LogValidationReviewInvalidFiles(section.Name, _package.Date);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois a revisão selecionada não possui arquivos válidos. ");
                        return Outcome(OutcomeNames.Cancel);
                    }
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogValidateSectionsFilesError(e);
                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com o cálculo de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro inesperado na validação das revisões da seção.");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private static bool AreFilesValid(Application.Apis._Shared.File dxf, Application.Apis._Shared.File sli)
        {
            return dxf != null
                    && !string.IsNullOrEmpty(dxf.Base64)
                    && sli != null
                    && !string.IsNullOrEmpty(sli.Base64);
        }
    }
}
