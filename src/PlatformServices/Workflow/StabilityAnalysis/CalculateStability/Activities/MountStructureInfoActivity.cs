using Application.Apis.Clients.Response.Instrument;
using Application.Apis.Clients.Response.Package.GetById;
using Application.Apis.Clients.Response.Section.GetById;
using Application.Apis.Clients.Response.Structure.GetStabilityAnalysisInfo;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class MountStructureInfoActivity : Activity
    {
        private readonly ILogger<MountStructureInfoActivity> _logger;

        public MountStructureInfoActivity(ILogger<MountStructureInfoActivity> logger)
        {
            _logger = logger;
        }

        protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
        {
            try
            {
                var sections = context.GetTransientVariable<List<GetSectionByIdResponse>>(PackageVariables.Sections);
                var instruments = context.GetTransientVariable<List<GetInstrumentByIdResponse>>(PackageVariables.Instruments);
                var structure = context.GetTransientVariable<GetStructureStabilityAnalysisInfoResponse>(PackageVariables.Structure);
                var package = context.GetTransientVariable<GetPackageByIdResponse>(PackageVariables.Package);

                var structureInfo = new StructureInfo
                {
                    StaticMaterialReviewDate = package.Date,
                    SeismicCoefficient = structure.SeismicCoefficient,
                    ShouldEvaluateDrainedCondition = structure.ShouldEvaluateDrainedCondition,
                    ShouldEvaluatePseudoStaticCondition = structure.ShouldEvaluatePseudoStaticCondition,
                    ShouldEvaluateUndrainedCondition = structure.ShouldEvaluateUndrainedCondition,
                    Slide2Configuration = structure.Slide2Configuration,
                    StructureId = structure.Id,
                    StructureName = structure.Name,
                    Sections = new()
                };

                foreach (var section in sections)
                {
                    var beachSection = package.Sections.First(packageSection => packageSection.Id == section.Id);
                    var beachLength = (double?)beachSection.BeachLength?.Length;

                    var filesResult = DetermineFilesToUse(package, section);

                    if (filesResult == null || filesResult.SectionReview == null || filesResult.Dxf == null || filesResult.Sli == null)
                    {
                        _logger.LogReviewNotFoundForSection(section.Name);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível montar as informações da estrutura na análise de estabilidade pois não há arquivos válidos para a seção {section.Name}.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var sectionInfo = new SectionInfo()
                    {
                        Coordinates = section.Coordinates,
                        IsSkew = section.IsSkew,
                        MinimumDrainedDepth = section.MinimumDrainedDepth,
                        MinimumPseudoStaticDepth = section.MinimumPseudoStaticDepth,
                        MinimumUndrainedDepth = section.MinimumUndrainedDepth,
                        SectionReviewData = filesResult,
                        BeachLength = beachLength,
                        SectionId = section.Id,
                        SectionName = section.Name,
                        UpstreamLinimetricRulerQuota = package.UpstreamLinimetricRuler.Quota,
                        DownstreamLinimetricRulerQuota = package.DownstreamLinimetricRuler.Quota,
                        Instruments = new()
                    };

                    var sectionInstruments = package.Sections.First(s => s.Id == section.Id).Instruments;

                    foreach (var instrument in sectionInstruments)
                    {
                        var instrumentInfo = instruments.First(i => i.Id == instrument.Id);

                        var instrumentData = new InstrumentData()
                        {
                            InstrumentId = instrument.Id,
                            MeasurementId = instrument.Measurement?.Id,
                            InstrumentType = instrumentInfo.Type,
                            InstrumentDryType = instrumentInfo.DryType,
                            LinimetricRulerPosition = instrumentInfo.LinimetricRulerPosition,
                            DryReading = instrument.ReadingValue?.Dry ?? false,
                            ReadingQuota = instrument.ReadingValue?.Quota ?? 0
                        };

                        sectionInfo.Instruments.Add(instrumentData);
                    }

                    structureInfo.Sections.Add(sectionInfo);
                }

                context.SetTransientVariable(Variables.StructureInfo, structureInfo);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogMountStructureInfoError(e);
                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível montar as informações da estrutura na análise de estabilidade.");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private SectionReviewData DetermineFilesToUse(GetPackageByIdResponse package, GetSectionByIdResponse section)
        {
            var packageSection = package.Sections.First(x => x.Id == section.Id);

            if (packageSection.SectionReview == null)
            {
                throw new Exception($"Section {section.Name} does not have valid reviews.");
            }

            var selectedReview = section.Reviews.FirstOrDefault(x => x.Id == packageSection.SectionReview.Id);

            if (packageSection.ConstructionStage != null)
            {
                var constructionStage = section.Reviews
                    .SelectMany(x => x.ConstructionStages)
                    .FirstOrDefault(x => x.Id == packageSection.ConstructionStage.Id);

                if (constructionStage != null && AreFilesValid(constructionStage.Drawing, constructionStage.Sli))
                {
                    return new()
                    {
                        SectionReview = selectedReview,
                        ConstructionStage = constructionStage,
                        Dxf = constructionStage.Drawing,
                        Sli = constructionStage.Sli
                    };
                }
            }
            else if (AreFilesValid(selectedReview.Drawing, selectedReview.Sli))
            {
                return new()
                {
                    SectionReview = selectedReview,
                    Dxf = selectedReview.Drawing,
                    Sli = selectedReview.Sli
                };
            }

            foreach (var review in section.Reviews.OrderByDescending(x => x.StartDate))
            {
                if (AreFilesValid(review.Drawing, review.Sli))
                {
                    return new()
                    {
                        SectionReview = review,
                        Dxf = review.Drawing,
                        Sli = review.Sli
                    };
                }
                else if (review.ConstructionStages.Any())
                {
                    var currentStage = review.ConstructionStages
                                .FirstOrDefault(x => x.IsCurrentStage);

                    if (currentStage != null && AreFilesValid(currentStage.Drawing, currentStage.Sli))
                    {
                        return new()
                        {
                            SectionReview = review,
                            ConstructionStage = currentStage,
                            Dxf = currentStage.Drawing,
                            Sli = currentStage.Sli
                        };
                    }

                    foreach (var stage in review.ConstructionStages.OrderByDescending(x => x.Stage))
                    {
                        if (AreFilesValid(stage.Drawing, stage.Sli))
                        {
                            return new()
                            {
                                SectionReview = review,
                                ConstructionStage = stage,
                                Dxf = stage.Drawing,
                                Sli = stage.Sli
                            };
                        }
                    }
                }
            }

            throw new Exception($"No valid files found for section {section.Name}");
        }

        private static bool AreFilesValid(Application.Apis._Shared.File dxf, Application.Apis._Shared.File sli)
        {
            return dxf != null
                    && !string.IsNullOrEmpty(dxf.Base64)
                    && sli != null
                    && !string.IsNullOrEmpty(sli.Base64);
        }
    }
}
