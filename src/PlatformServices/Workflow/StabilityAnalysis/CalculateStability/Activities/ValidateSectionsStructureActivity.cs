using Application.Apis.Clients.Response.Package.GetById;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Serilog;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class ValidateSectionsStructureActivity : Activity
    {
        private GetPackageByIdResponse _package;

        protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
        {
            try
            {
                _package = context.GetTransientVariable<GetPackageByIdResponse>(PackageVariables.Package);
                var sections = context.GetTransientVariable<List<Application.Apis.Clients.Response.Section.GetById.GetSectionByIdResponse>>(PackageVariables.Sections);

                var allSectionsHasSameStructure = sections.All(x => x.Structure.Id == sections.First().Structure.Id);

                if (!allSectionsHasSameStructure)
                {
                    context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois as seções não pertencem à mesma estrutura.");
                    return Outcome(OutcomeNames.Cancel);
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in ValidateSectionsStructureActivity");
                context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade do pacote com data {_package.Date:dd/MM/yyyy HH:mm} pois ocorreu um erro inesperado na validação das estruturas das seções.");
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
