using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class GetSectionsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GetSectionsActivity> _logger;

        public GetSectionsActivity(
            IClientsApiService clientsApiService,
            ILogger<GetSectionsActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sectionIds = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).SectionIds;
                var sections = new List<Application.Apis.Clients.Response.Section.GetById.GetSectionByIdResponse>();

                foreach (var sectionId in sectionIds)
                {
                    var section = await _clientsApiService.GetSectionById(sectionId);

                    if (section == null)
                    {
                        SectionLogging.LogSectionNotFound(_logger, sectionId);
                        return Outcome(OutcomeNames.Cancel);
                    }

                    sections.Add(section);
                }

                context.SetTransientVariable(Variables.Sections, sections);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogGetSectionsError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
