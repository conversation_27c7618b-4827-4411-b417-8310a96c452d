using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using PackageVariables = Workflow.Constants.PackageWorkFlow.Variables;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class GetStructureActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GetStructureActivity> _logger;

        public GetStructureActivity(IClientsApiService clientsApiService, ILogger<GetStructureActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var sections = context.GetTransientVariable<List<Application.Apis.Clients.Response.Section.GetById.GetSectionByIdResponse>>(PackageVariables.Sections);
                var section = sections.First();

                var structure = await _clientsApiService.GetStructureStabilityAnalysisInfo(section.Structure.Id);

                if (structure == null)
                {
                    _logger.LogStructureNotFound(section.Structure.Id);
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(PackageVariables.Structure, structure);
                context.SetTransientVariable(Variables.StructureId, structure.Id);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogGetStructureError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
