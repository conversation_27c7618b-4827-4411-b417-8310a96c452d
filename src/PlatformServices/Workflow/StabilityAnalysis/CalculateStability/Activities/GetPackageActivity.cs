using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class GetPackageActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GetPackageActivity> _logger;

        public GetPackageActivity(
            IClientsApiService clientsApiService,
            ILogger<GetPackageActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var packageId = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).PackageId;

                var package = await _clientsApiService.GetPackageById(packageId);

                if (package == null)
                {
                    _logger.LogPackageNotFound(packageId);
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(Variables.Package, package);

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogGetPackageError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
