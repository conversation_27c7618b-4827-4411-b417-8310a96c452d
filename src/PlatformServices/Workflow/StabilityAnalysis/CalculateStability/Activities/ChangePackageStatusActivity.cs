using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa.ActivityResults;
using Elsa.Services;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.PackageWorkFlow;

namespace Workflow.StabilityAnalysis.CalculateStability.Activities
{
    public class ChangePackageStatusActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<ChangePackageStatusActivity> _logger;

        public ChangePackageStatusActivity(
            IClientsApiService clientsApiService,
            ILogger<ChangePackageStatusActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var packageId = (context.GetTransientVariable<Domain.Messages.Commands.Package.CalculateStability>(Variables.Command)).PackageId;

                var entityHasBeenUpdated = await _clientsApiService.UpdatePackageStatus(new()
                {
                    Id = packageId,
                    Status = Domain.Enums.PackageStatus.Failed
                });

                if (!entityHasBeenUpdated)
                {
                    _logger.LogPackageStatusUpdateProblem(packageId);
                    return Fault($"There was a problem updating package {packageId} status");
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogChangePackageStatusError(e);
                return Fault(e);
            }
        }
    }
}
