using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.MassTransit;
using Elsa.Activities.Primitives;
using Elsa.Builders;
using Workflow.StabilityAnalysis._SharedActivities;
using Workflow.StabilityAnalysis.CalculateStability.Activities;
using Workflow.StabilityAnalysis.StartSimulation.Activities;
using static Workflow.Constants.StabilityAnalysisWorkFlow;

namespace Workflow.Package.CalculateStability
{
    public class CalculateStabilityWorkflow : IWorkflow
    {
        public void Build(IWorkflowBuilder builder)
        {
            builder
                .WithDisplayName("Calculate stability workflow")
                .ReceiveMassTransitMessage(activity => activity.Set(x => x.MessageType, x => typeof(Domain.Messages.Commands.Package.CalculateStability)))
                .WithDisplayName("Receive calculate stability message")
                .WithDescription("When receive the message, will get the package.")
                .SetTransientVariable(Variables.Command, context => context.GetInput<Domain.Messages.Commands.Package.CalculateStability>())
                .Then<GetPackageActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<GetSectionsActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<GetStructureActivity>(activity =>
                {
                    activity
                    .When(OutcomeNames.Cancel)
                    .Then<ChangePackageStatusActivity>()
                    .Finish();
                })
                .Then<ValidateSectionsFilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<ValidateSectionsStructureActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<GetInstrumentsActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<MountStructureInfoActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<MountSliFilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<CheckDrainageAlertActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<MountSltmFilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<GetS01FilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<ExtractInformationFromS01FilesActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<CreateDrainageAlertActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                })
                .Then<CheckSlopeLimitWarningActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangeSimulationStatusActivity>()
                        .Finish();
                })
                .Then<CreateStabilityAnalysisActivity>(activity =>
                {
                    activity
                        .When(OutcomeNames.Cancel)
                        .Then<CreateStabilityAnalysisErrorNotificationActivity>()
                        .Then<ChangePackageStatusActivity>()
                        .Finish();
                });
        }
    }
}
