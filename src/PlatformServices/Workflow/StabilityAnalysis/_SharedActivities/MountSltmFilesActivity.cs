using System.Drawing;
using Domain.Enums;
using Dxf.Core.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using Serilog;
using Slide.Core;
using Slide.Core.Objects.Sli;
using Slide.Core.Objects.Sltm;
using Workflow.StabilityAnalysis.Classes;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis._SharedActivities
{
    public class MountSltmFilesActivity : Activity
    {
        private double _defaultWidthPerChar = 4;
        private double _defaultMaxVerticalDistance = 8;
        private double _defaultCollisionOffsetY = 2;

        private double _widthPerChar;
        private double _maxVerticalDistance;
        private double _collisionOffsetY;

        protected override IActivityExecutionResult OnExecute(ActivityExecutionContext context)
        {
            try
            {
                var sliFiles = context.GetTransientVariable<List<SliDetailAggregator>>(Variables.SliFiles);

                foreach (var sliFile in sliFiles)
                {
                    var allPoints = sliFile.DxfFile.GetAllPoints();

                    var scaleFactor = allPoints.Max(x => x.X) / 100.0;

                    _widthPerChar = _defaultWidthPerChar * scaleFactor;
                    _maxVerticalDistance = _defaultMaxVerticalDistance * scaleFactor;
                    _collisionOffsetY = _defaultCollisionOffsetY * scaleFactor;

                    var sltmFile = CreateSltmFile(sliFile);
                    sliFile.SltmFile = sltmFile;
                }

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in MountSltmFilesActivity");
                context.SetTransientVariable(Variables.ErrorMessage, "Não conseguimos continuar com a análise de estabilidade porque houve um erro ao montar os arquivos necessários.");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private SltmFile CreateSltmFile(SliDetailAggregator sliFile)
        {
            var sltmFile = new SltmFile();

            CreateToolsForInstruments(sltmFile, sliFile.Instruments.Piezometers.Concat(sliFile.Instruments.WaterLevelIndicators).ToList());
            SetToolOrderWithinLayer(sltmFile);

            return sltmFile;
        }

        private void CreateToolsForInstruments(SltmFile sltmFile, List<Instrument> instruments)
        {
            foreach (var instrument in instruments.OrderBy(x => x.PointD.X))
            {
                if (instrument.InstrumentInfo == null)
                {
                    continue;
                }

                var y1 = (double)(
                    instrument.InstrumentInfo.BaseQuota
                    ?? (instrument.InstrumentInfo.Measurements != null && instrument.InstrumentInfo.Measurements.Any()
                        ? instrument.InstrumentInfo.Measurements.Min(x => x.Quota)
                        : 0));
                var y2 = (double)instrument.InstrumentInfo.TopQuota;

                var textBoxesEndCoords = sltmFile.ToolsData.Tools
                    .Where(x => x.ToolType == Slide.Core.Enums.ToolType.TextBoxes)
                    .Select(x => x.End)
                    .ToList();

                var connectLineTop = y2 + (y2 * 0.05);

                var newCoords = CheckAndResolveCollision(textBoxesEndCoords,
                    new(instrument.PointD.X, connectLineTop), instrument.InstrumentInfo.Identifier.Length * _widthPerChar);

                var color = instrument.InstrumentInfo.Type == InstrumentType.WaterLevelIndicator ? Color.DarkBlue : Color.Red;

                sltmFile.ToolsData.Tools.Add(CreateToolForInstrument(instrument, y1, y2, color));
                sltmFile.ToolsData.Tools.Add(CreateToolForConnectLine(instrument, y2, newCoords.Y, color));
                sltmFile.ToolsData.Tools.Add(CreateToolForTextBox(instrument, sltmFile.ToolsData.Tools, newCoords.Y, newCoords.Y, color));
            }
        }

        private void SetToolOrderWithinLayer(SltmFile sltmFile)
        {
            sltmFile.ToolsData.Tools = sltmFile.ToolsData.Tools.OrderBy(x => x.ToolType).ToList();

            // Set ToolOrderWithinLayer because the layer order is important to put the text boxes in front of the lines
            for (int i = 0; i < sltmFile.ToolsData.Tools.Count; i++)
            {
                sltmFile.ToolsData.Tools[i].ToolOrderWithinLayer = i + 1;
            }
        }

        private Tool CreateToolForTextBox(Instrument instrument, List<Tool> tools, double y1, double y2, Color color)
        {
            var tool = new Tool()
            {
                ToolType = Slide.Core.Enums.ToolType.TextBoxes,
                Start = new() { X = instrument.PointD.X, Y = y1 },
                End = new() { X = instrument.PointD.X, Y = y2 },
                VerticesStart = new()
                {
                    DPoints = new()
                    {
                        new() { Index = 0, X = instrument.PointD.X, Y = y1 },
                        new() { Index = 1, X = instrument.PointD.X, Y = y2 }
                    }
                },
                EntityColor = color,
                FillColor = color,
                TextAlign = 544,
                LineWeight = 1,
                IsExteriorLine = false,
                IsFilled = true,
                ScaleWithZoom = false,
                Text1 = $"{instrument.InstrumentInfo?.Identifier}"
            };

            return tool;
        }

        private Tool CreateToolForConnectLine(Instrument instrument, double y1, double y2, Color color)
        {
            return new()
            {
                ToolType = Slide.Core.Enums.ToolType.Polylines,
                Start = new() { X = instrument.PointD.X, Y = y1 },
                End = new() { X = instrument.PointD.X, Y = y2 },
                VerticesStart = new()
                {
                    DPoints = new()
                    {
                        new() { Index = 0, X = instrument.PointD.X, Y = y1 },
                        new() { Index = 1, X = instrument.PointD.X, Y = y2 }
                    }
                },
                EntityColor = color,
                FillColor = color,
                LineWeight = 1
            };
        }

        private Tool CreateToolForInstrument(Instrument instrument, double y1, double y2, Color color)
        {
            return new Tool
            {
                ToolType = Slide.Core.Enums.ToolType.Polylines,
                Start = new PointD { X = instrument.PointD.X, Y = y1 },
                End = new PointD { X = instrument.PointD.X, Y = y2 },
                VerticesStart = new()
                {
                    DPoints = new()
                    {
                        new DPoint { Index = 0, X = instrument.PointD.X, Y = y1 },
                        new DPoint { Index = 1, X = instrument.PointD.X, Y = y2 }
                    }
                },
                EntityColor = color,
                FillColor = color
            };
        }

        private PointD CheckAndResolveCollision(List<PointD> coordinates, PointD specificCoordinate, double width)
        {
            var resultCoordinate = new PointD { X = specificCoordinate.X, Y = specificCoordinate.Y };

            while (true)
            {
                var hasCollision = false;

                foreach (var coordinate in coordinates)
                {
                    var xDistance = Math.Abs(resultCoordinate.X - coordinate.X);
                    var yDistance = Math.Abs(resultCoordinate.Y - coordinate.Y);

                    // If the xDistance is less than the width and yDistance is less than maxVerticalDistance
                    // then we have a collision. In that case, increment resultCoordinate's Y by CollisionOffsetY value and set hasCollision to true.
                    if (xDistance < width && yDistance < _maxVerticalDistance)
                    {
                        resultCoordinate.Y += _collisionOffsetY;
                        hasCollision = true;

                        break;
                    }
                }

                if (!hasCollision)
                {
                    break;
                }
            }

            return resultCoordinate;
        }
    }
}
