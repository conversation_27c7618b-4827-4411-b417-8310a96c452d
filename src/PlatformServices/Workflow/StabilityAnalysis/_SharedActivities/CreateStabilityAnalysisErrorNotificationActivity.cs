using Application.Apis.Clients;
using Application.Observability.Logs;
using Domain.Enums;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.StabilityAnalysis._SharedActivities
{
    public class CreateStabilityAnalysisErrorNotificationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<CreateStabilityAnalysisErrorNotificationActivity> _logger;

        public CreateStabilityAnalysisErrorNotificationActivity(
            IClientsApiService clientsApiService,
            ILogger<CreateStabilityAnalysisErrorNotificationActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var errorMessage = context.GetTransientVariable<string>(Variables.ErrorMessage);

                if (string.IsNullOrEmpty(errorMessage))
                {
                    return Done();
                }

                var structureId = context.GetTransientVariable<Guid>(Variables.StructureId);

                if (structureId == Guid.Empty)
                {
                    return Done();
                }

                var users = await _clientsApiService.GetUsersAsync(new()
                {
                    StructureId = structureId,
                    Active = true,
                    Roles = new()
                    {
                        (int)Role.SuperSupport,
                        (int)Role.Support,
                        (int)Role.SuperAdministrator,
                        (int)Role.Administrator,
                        (int)Role.Operator,
                        (int)Role.Auditor
                    }
                });

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var result = await _clientsApiService.AddNotification(new()
                {
                    Users = users.Select(x => x.Id).ToList(),
                    NotificationMessage = errorMessage,
                    NotificationTheme = NotificationTheme.StabilityAnalysis
                });

                if (result == null || !result.IsSuccessStatusCode)
                {
                    _logger.LogCreateNotificationError(errorMessage);
                    return Fault("Error creating notification");
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogCreateStabilityAnalysisErrorNotificationError(e);
                return Fault(e);
            }
        }
    }
}
