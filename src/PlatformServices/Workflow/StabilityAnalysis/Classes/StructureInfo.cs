using Application.Apis.Clients.Response._Shared;

namespace Workflow.StabilityAnalysis.Classes
{
    public sealed class StructureInfo
    {
        public DateTime? StaticMaterialReviewDate { get; set; }
        public Guid StructureId { get; set; }
        public string StructureName { get; set; }
        public Slide2Configuration Slide2Configuration { get; set; }
        public double? SafetyFactorTarget { get; set; }
        public Orientation SeismicCoefficient { get; set; }
        public bool ShouldEvaluateDrainedCondition { get; set; }
        public bool ShouldEvaluatePseudoStaticCondition { get; set; }
        public bool ShouldEvaluateUndrainedCondition { get; set; }
        public List<SectionInfo> Sections { get; set; }
    }
}
