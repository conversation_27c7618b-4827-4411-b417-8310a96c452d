using Domain.Enums;
using IxMilia.Dxf;
using Slide.Core;

namespace Workflow.StabilityAnalysis.Classes
{
    public class SliDetailAggregator
    {
        public SliFile SliFile { get; set; }
        public SltmFile SltmFile { get; set; }
        public string S01 { get; set; }
        public bool PipingAlertGenerated { get; set; }
        public SliFileType SliFileType { get; set; }
        public DxfFile DxfFile { get; set; }
        public SectionInfo Section { get; set; }
        public InstrumentPoints Instruments { get; set; }
        public StructureInfo Structure { get; set; }
        public List<SafetyFactorValue> Values { get; set; }
        public List<string> Warnings { get; set; } = new();
    }
}
