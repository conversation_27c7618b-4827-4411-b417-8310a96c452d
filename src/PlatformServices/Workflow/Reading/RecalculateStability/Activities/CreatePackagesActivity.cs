using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Refit;
using static Workflow.Constants.ReadingWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Reading.RecalculateStability.Activities
{
    [Activity(
        Category = "Reading",
        DisplayName = "Create Packages",
        Description = "Will create one or more packages with the reading values.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class CreatePackagesActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public CreatePackagesActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var readingValues = context.GetTransientVariable<IEnumerable<Guid>>(Variables.RelatedReadingValuesIds);
                var packageRequest = new AddPackageRequest(readingValues);

                var response = await _clientsApiService.AddPackage(packageRequest);
                if (response == null)
                {
                    return Fault("Could not create the packages based on the reading values.");
                }

                context.JournalData.Add(Variables.CreatedPackages, response);
                context.SetTransientVariable(Variables.PackageIds, response);

                return Outcome(OutcomeNames.Done);
            }
            catch (ApiException e)
            {
                return Fault(e.Content);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
