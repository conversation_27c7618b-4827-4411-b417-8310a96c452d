using Application.Apis.Clients;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Refit;
using static Workflow.Constants.ReadingWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.Reading.RecalculateStability.Activities
{
    [Activity(
        Category = "Reading",
        DisplayName = "Get Related Reading Values",
        Description = "Will get all reading values related to the section where the reading was updated.",
        Outcomes = new[] { OutcomeNames.Done, OutcomeNames.Cancel }
    )]
    public sealed class GetRelatedReadingValuesActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;

        public GetRelatedReadingValuesActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var readingEvent = context.GetTransientVariable<Domain.Messages.Events.Reading.ReadingUpdated>(Variables.ReadingEvent);
                if (!readingEvent.ShouldRecalculateStability)
                {
                    context.JournalData.SetItem(Variables.Output, "This reading updated event is not set to recalculate stability.");
                    return Outcome(OutcomeNames.Cancel);
                }

                var relatedReadingValues = await _clientsApiService
                    .GetRelatedReadingValues(readingEvent.Id);

                if (relatedReadingValues == null || !relatedReadingValues.Any())
                {
                    context.JournalData.SetItem(Variables.Output, "This reading updated event does not have related reading values.");
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(Variables.RelatedReadingValuesIds, relatedReadingValues);
                context.JournalData.SetItem(Variables.RelatedReadingValuesIds, relatedReadingValues);

                return Outcome(OutcomeNames.Done);
            }
            catch (ApiException e)
            {
                return Fault(e.Content);
            }
            catch (Exception e)
            {
                return Fault(e);
            }
        }
    }
}
