using Application.Apis.Clients.Response.Clients;
using Application.Observability.Logs;
using Application.Sftp;
using Application.Sftp.Model.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.ImportReadings.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Get pending reading files",
        Description = "Get pending reading files from each client on SFTP server",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False }
    )]
    public class GetPendingReadingFilesActivity : Activity
    {
        private readonly ISftpService _sftpService;
        private readonly ILogger<GetPendingReadingFilesActivity> _logger;

        public GetPendingReadingFilesActivity(
            ISftpService sftpService,
            ILogger<GetPendingReadingFilesActivity> logger)
        {
            _sftpService = sftpService;
            _logger = logger;
        }
        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var filesLastFetchDate = context.GetTransientVariable<DateTime>(
                    name: Variables.FilesLastFetchDate);

                var filesResult = await _sftpService.GetAllFilesAsync(
                    new GetAllFilesRequest(
                       FilesLastFetchDate: filesLastFetchDate,
                       SourcePaths: clientConfiguration.FtpRootPath.GetSourcePaths()));

                if (!filesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error getting files",
                        value: filesResult);

                    context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro ao listar os arquivos do servidor SFTP.");
                    _logger.LogListingFilesFromSftpError(filesResult.Message, filesResult.Exception);
                    return Outcome(OutcomeNames.Cancel);
                }

                var downloadedFilesResult = await _sftpService.BulkDownloadFilesAsync(
                    new DownloadFilesRequest(
                        Files: filesResult.Data.Files));

                if (!downloadedFilesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error downloading files",
                        value: downloadedFilesResult);

                    context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro ao baixar os arquivos do servidor SFTP.");
                    _logger.LogDownloadingFilesFromSftpError(downloadedFilesResult.Message, downloadedFilesResult.Exception);
                    return Outcome(OutcomeNames.Cancel);
                }

                context.SetTransientVariable(
                    name: Variables.DownloadedFiles,
                    value: downloadedFilesResult.Data);

                context.SetTransientVariable(
                    name: Variables.TemplateType,
                    value: clientConfiguration.TemplateType);

                return Outcome(OutcomeNames.True);
            }
            catch (Exception e)
            {
                context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro inesperado ao obter os arquivos para processar no servidor SFTP.");
                _logger.LogGetPendingFilesUnexpectedError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
