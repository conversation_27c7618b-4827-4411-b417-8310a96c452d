using System.Text.Json;
using Application.Apis.Clients;
using Application.Apis.Clients.Request;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.ImportReadings.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Send converted readings",
        Description = "Sends the processed readings to the API clients for creation.",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class SendReadingsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<SendReadingsActivity> _logger;

        public SendReadingsActivity(
            IClientsApiService clientsApiService,
            ILogger<SendReadingsActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var readingsToBeSent = context
                    .GetTransientVariable<IEnumerable<AddReadingRequest>>(
                    name: Variables.ReadingsToBeSent);

                if (readingsToBeSent == null || !readingsToBeSent.Any())
                {
                    context.JournalData.Add(
                        key: nameof(readingsToBeSent),
                        value: "No readings were found to be created. Check previous activities");

                    _logger.LogNoPayloadsToSend();
                    return Done();
                }

                var response = await _clientsApiService
                    .AddReadingsAsync(readingsToBeSent);

                if (!response.IsSuccessStatusCode)
                {
                    context.JournalData.Add(
                        key: $"Error: {response.Error?.Content ?? response.Error?.Message}",
                        value: response.Error);

                    var errorResponses = JsonSerializer.Deserialize<IEnumerable<JsonElement>>(response.Error.Content.ToString());

                    var errorMessage = errorResponses != null
                        ? string.Join("; ", errorResponses.Select(e =>
                          e.TryGetProperty("message", out JsonElement messageElement)
                                ? messageElement.GetString() ?? "Mensagem de erro não encontrada"
                                : "Mensagem de erro não encontrada"))
                        : response.Error?.Message;

                    context.SetTransientVariable(Variables.ErrorMessage, errorMessage);
                    _logger.LogReadingCreatingError(errorMessage);
                    return Outcome(OutcomeNames.Cancel);
                }

                return Done();
            }
            catch (Exception e)
            {
                context.SetTransientVariable(Variables.ErrorMessage, $"Ocorreu um erro inesperado ao inserir as leituras no banco de dados.");
                _logger.LogReadingCreatingUnexpectedError(e);
                return Outcome(OutcomeNames.Cancel);
            }
        }
    }
}
