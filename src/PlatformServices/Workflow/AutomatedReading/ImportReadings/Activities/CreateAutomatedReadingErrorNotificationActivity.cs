using Application.Apis.Clients;
using Application.Apis.Clients.Response.Clients;
using Application.Observability.Logs;
using Domain.Enums;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Activity = Elsa.Services.Activity;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.ImportReadings.Activities
{
    public class CreateAutomatedReadingErrorNotificationActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<CreateAutomatedReadingErrorNotificationActivity> _logger;

        public CreateAutomatedReadingErrorNotificationActivity(
            IClientsApiService clientsApiService,
            ILogger<CreateAutomatedReadingErrorNotificationActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var errorMessage = context.GetTransientVariable<string>(Variables.ErrorMessage);

                if (string.IsNullOrEmpty(errorMessage))
                {
                    return Done();
                }

                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                if (clientConfiguration == null || clientConfiguration.ClientId == Guid.Empty)
                {
                    return Done();
                }

                var notificationMessage = $"Erro na importação das leituras automáticas do cliente {clientConfiguration.ClientName}: {errorMessage}";

                var notificationExists = await _clientsApiService.CheckIfNotificationExists(new()
                {
                    Message = notificationMessage,
                    Theme = NotificationTheme.AutomatedReading,
                    StartDate = DateTime.UtcNow.AddHours(-12),
                    EndDate = DateTime.UtcNow
                });

                if (notificationExists)
                {
                    return Done();
                }

                var users = await _clientsApiService.GetUsersAsync(new()
                {
                    ClientId = clientConfiguration.ClientId,
                    Active = true,
                    Roles = new()
                    {
                        (int)Role.SuperSupport,
                        (int)Role.Support,
                        (int)Role.SuperAdministrator,
                        (int)Role.Administrator
                    }
                });

                if (users == null || !users.Any())
                {
                    return Done();
                }

                var result = await _clientsApiService.AddNotification(new()
                {
                    Users = users.Select(x => x.Id).ToList(),
                    NotificationMessage = notificationMessage,
                    NotificationTheme = NotificationTheme.AutomatedReading
                });

                if (result == null || !result.IsSuccessStatusCode)
                {
                    _logger.LogCreateNotificationError();
                    return Fault("Error creating notification");
                }

                return Done();
            }
            catch (Exception e)
            {
                _logger.LogCreateNotificationUnexpectedError(e);
                return Fault(e);
            }
        }
    }
}
