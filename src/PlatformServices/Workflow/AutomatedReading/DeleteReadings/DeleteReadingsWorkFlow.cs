using Application.Apis.Clients.Response.Clients;
using Application.AutomatedReading;
using CronExpressionDescriptor;
using Elsa;
using Elsa.Activities.ControlFlow;
using Elsa.Activities.Temporal;
using Elsa.Builders;
using Microsoft.Extensions.Options;
using Workflow.AutomatedReading._SharedActivities;
using Workflow.AutomatedReading.DeleteReadings.Activities;
using static Workflow.Constants.AutomatedReadingsWorkFlow;

namespace Workflow.AutomatedReading.DeleteReadings;

public sealed class DeleteReadingsWorkFlow : IWorkflow
{
    private readonly AutomatedReadingOptions _automatedReadingOptions;

    public DeleteReadingsWorkFlow(
        IOptions<AutomatedReadingOptions> automatedReadingOptions)
    {
        _automatedReadingOptions = automatedReadingOptions.Value;
    }

    public void Build(IWorkflowBuilder builder)
    {
        var readableCronExpression = ExpressionDescriptor
            .GetDescription(_automatedReadingOptions.DeleteReadingsCronExpression);

        builder
            .WithDeleteCompletedInstances(true)
            .AsSingleton()
            .WithDisplayName("Purges processed files via SFTP")
            .Cron(_automatedReadingOptions.DeleteReadingsCronExpression)
            .WithDisplayName($"Runs {readableCronExpression}")
            .Then<GetClientConfigurationsActivity>(activityBuilder =>
            {
                activityBuilder
                    .When(OutcomeNames.False)
                    .Finish();

                activityBuilder
                    .When(OutcomeNames.True)
                    .ForEach(items: context => context
                            .GetTransientVariable<List<GetClientAutomatedReadingsConfigurationsResponse>>(
                                Variables.ClientConfigurations)!,
                        iterate: item =>
                        {
                            item.Then<DeleteProcessedFilesActivity>();
                        }
                    )
                    .When(OutcomeNames.Done);
            });
    }
}