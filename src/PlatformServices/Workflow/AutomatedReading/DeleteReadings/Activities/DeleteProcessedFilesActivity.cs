using Application.Apis.Clients.Response.Clients;
using Application.AutomatedReading;
using Application.Observability.Logs;
using Application.Sftp;
using Application.Sftp.Model.Request;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading.DeleteReadings.Activities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Delete processed files",
        Description = "Deletes previously processed files..",
        Outcomes = new[] { OutcomeNames.Done }
    )]
    public sealed class DeleteProcessedFilesActivity : Activity
    {
        private readonly ISftpService _sftpService;
        private readonly AutomatedReadingOptions _automatedReadingOptions;
        private readonly ILogger<DeleteProcessedFilesActivity> _logger;

        public DeleteProcessedFilesActivity(
            ISftpService sftpService,
            IOptions<AutomatedReadingOptions> automatedReadingOptions,
            ILogger<DeleteProcessedFilesActivity> logger)
        {
            _sftpService = sftpService;
            _automatedReadingOptions = automatedReadingOptions.Value;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfiguration = context
                    .GetInput<GetClientAutomatedReadingsConfigurationsResponse>();

                var processedFullPath = string.Format(
                    SftpProcessedFolderName,
                    clientConfiguration.FtpRootPath.Root);

                var deleteFilesResult = await _sftpService.DeleteFilesAsync(
                    new DeleteFilesRequest(
                        SftpFilesPurgeDate: DateTime.UtcNow.AddDays(
                            -_automatedReadingOptions.SftpFilesPurgeDays),
                        SourcePath: processedFullPath));

                if (!deleteFilesResult.IsSuccessful)
                {
                    context.JournalData.Add(
                        key: "Error deleting files",
                        value: deleteFilesResult);
                    
                    _logger.LogFileDeletionError(deleteFilesResult.Message, deleteFilesResult.Exception);
                    return Fault("Failed to delete files");
                }

                return Done();
            }
            catch (Exception exception)
            {
                _logger.LogFileDeletionUnexpectedError(exception);
                return Fault(exception);
            }
        }
    }
}
