using Application.Apis.Clients;
using Application.Observability.Logs;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Attributes;
using Elsa.Services.Models;
using Microsoft.Extensions.Logging;
using static Workflow.Constants.AutomatedReadingsWorkFlow;
using Activity = Elsa.Services.Activity;

namespace Workflow.AutomatedReading._SharedActivities
{
    [Activity(
        Category = "Automated Readings",
        DisplayName = "Get active client automated readings configurations",
        Description = "Get the active configurations to search for files on the SFTP server",
        Outcomes = new[] { OutcomeNames.True, OutcomeNames.False }
    )]
    public sealed class GetClientConfigurationsActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly ILogger<GetClientConfigurationsActivity> _logger;

        public GetClientConfigurationsActivity(
            IClientsApiService clientsApiService,
            ILogger<GetClientConfigurationsActivity> logger)
        {
            _clientsApiService = clientsApiService;
            _logger = logger;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(
            ActivityExecutionContext context)
        {
            try
            {
                var clientConfigurations = await _clientsApiService
                    .GetEnabledClientAutomatedReadingsConfigurationsAsync();

                if (clientConfigurations == null || !clientConfigurations.Any())
                {
                    _logger.LogClientConfigurationsNotFound();
                    return Outcome(OutcomeNames.False);
                }

                context.SetTransientVariable(
                    name: Variables.ClientConfigurations,
                    value: clientConfigurations);

                context.SetTransientVariable(
                    name: Variables.FilesLastFetchDate,
                    value: DateTime.UtcNow);

                return Outcome(OutcomeNames.True);
            }
            catch (Exception exception)
            {
                _logger.LogClientConfigurationsUnexpectedError(exception);
                return Fault(exception);
            }
        }
    }
}
