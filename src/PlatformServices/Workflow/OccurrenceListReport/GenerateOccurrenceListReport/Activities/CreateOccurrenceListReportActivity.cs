using Application.Apis.Clients;
using Application.Apis.Clients.Request.Occurrence;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Microsoft.AspNetCore.Hosting;
using Workflow.OccurrenceListReport.GenerateOccurrenceListReport.Activities.Classes;
using static Workflow.Constants.OccurrenceListReportWorkflow;
using Activity = Elsa.Services.Activity;

namespace Workflow.OccurrenceListReport.GenerateOccurrenceListReport.Activities
{
    public class CreateOccurrenceListReportActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private readonly IWebHostEnvironment _webHostEnvironment;

        public CreateOccurrenceListReportActivity(
            IClientsApiService clientsApiService,
            IWebHostEnvironment webHostEnvironment)
        {
            _clientsApiService = clientsApiService;
            _webHostEnvironment = webHostEnvironment;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var reportId = context.GetInput<Domain.Messages.Events.OccurrenceListReport.OccurrenceListReportCreated>().Id;

                var report = await _clientsApiService.GetOccurrenceListReportAsync(reportId);

                if (report == null)
                {
                    return Fault("Report not found");
                }

                var query = new SearchOccurrenceUnpaginatedRequest()
                {
                    DateFilter = report.DateFilter,
                    DaysRemaining = report.DaysRemaining,
                    EndDate = report.EndDate,
                    InspectionSheetType = report.InspectionSheetType,
                    StartDate = report.StartDate,
                    Status = report.Status,
                    WithActionPlan = report.WithActionPlan
                };

                var occurrences = await _clientsApiService.SearchOccurrenceUnpaginatedAsync(
                    report.Structures.Select(x => x.Id).ToList(), query);

                if (occurrences == null)
                {
                    return Done();
                }

                var logisoilLogoPath = Path.Combine(_webHostEnvironment.WebRootPath, Constants.ImagesPath, Constants.LogisoilLogoFileName);

                // TODO: Scrape the structure map on the front-end when the map is deployed
                var structureImagePath = Path.Combine(_webHostEnvironment.WebRootPath, Constants.ImagesPath, Constants.TempStructureImageFileName);

                var clientsGrouped = occurrences
                    .GroupBy(o => new { o.ClientId })
                    .Select(clientGroup => new OccurrencesByClient
                    {
                        ClientId = clientGroup.Key.ClientId,
                        LogisoilLogoPath = logisoilLogoPath,
                        Structures = clientGroup
                            .GroupBy(o => new { o.StructureId, o.StructureName })
                            .Select(structGroup => new OccurrencesByStructure(
                                structGroup.Key.StructureId,
                                structGroup.Key.StructureName,
                                structureImagePath,
                                structGroup.ToList()))
                            .ToList()
                    })
                    .ToList();

                var pdfs = new List<byte[]>();

                foreach (var client in clientsGrouped)
                {
                    var generator = new OccurrenceListReportGenerator();

                    var clientLogo = await _clientsApiService.GetClientLogoAsync(client.ClientId);

                    if (clientLogo?.Logo != null && !string.IsNullOrEmpty(clientLogo.Logo.Base64))
                    {
                        var fileType = clientLogo.Logo.Name?.Split('.').LastOrDefault() ?? "png";
                        var imageBytes = Convert.FromBase64String(clientLogo.Logo.Base64);
                        client.ClientLogoPath = Path.Combine(_webHostEnvironment.WebRootPath, Constants.ImagesPath, $"{Guid.NewGuid()}.{fileType}");

                        File.WriteAllBytes(client.ClientLogoPath, imageBytes);
                    }

                    var pdf = generator.Generate(client, query);

                    if (!string.IsNullOrEmpty(client.ClientLogoPath) && File.Exists(client.ClientLogoPath))
                    {
                        File.Delete(client.ClientLogoPath);
                    }

                    pdfs.Add(pdf);
                }

                var occurrenceListReport = new OccurrenceListReportModel()
                {
                    Configuration = report,
                    Occurrences = occurrences,
                    Pdfs = pdfs
                };

                context.SetTransientVariable(Variables.OccurrenceListReportModel, occurrenceListReport);

                return Done();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw;
            }
        }
    }
}
